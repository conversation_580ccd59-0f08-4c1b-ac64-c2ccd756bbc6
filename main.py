import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from login_window import LoginWindow


class Application:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.login_window = None
        self.main_window = None
        
    def start(self):
        """启动应用程序"""
        self.show_login()
        return self.app.exec_()
    
    def show_login(self):
        """显示登录窗口"""
        self.login_window = LoginWindow()
        self.login_window.login_success.connect(self.on_login_success)
        self.login_window.show()
    
    def on_login_success(self):
        """登录成功处理"""
        print("登录成功！准备进入主界面...")
        self.login_window.close()
        
        # 这里可以显示主窗口（1000x800）
        # self.show_main_window()
        
        # 暂时显示一个消息框确认登录成功
        msg = QMessageBox()
        msg.setWindowTitle("登录成功")
        msg.setText("恭喜！您已成功登录系统。\n\n提示：鼠标在窗口内时按空格键可以登录。")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
        
        # 关闭应用程序
        self.app.quit()
    
    def show_main_window(self):
        """显示主窗口（1000x800）"""
        # 这里将来会创建主窗口
        pass


if __name__ == "__main__":
    app = Application()
    sys.exit(app.start())
