import sys
from PyQt5.QtWidgets import (<PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QFrame, QSpacerItem, 
                             QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QBrush, QIcon


class LoginWindow(QWidget):
    login_success = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.mouse_inside = False
        self.space_pressed = False
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("用户登录")
        self.setFixedSize(800, 600)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置背景色
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
        """)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # 顶部空白
        main_layout.addItem(QSpacerItem(20, 80, QSizePolicy.Minimum, QSizePolicy.Fixed))
        
        # Logo区域
        logo_layout = QHBoxLayout()
        logo_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        logo_label = QLabel("应用登录")
        logo_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        logo_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_label)
        
        logo_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        main_layout.addLayout(logo_layout)
        
        # 登录表单区域
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #ddd;
            }
        """)
        form_frame.setFixedSize(400, 300)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setContentsMargins(40, 40, 40, 40)
        form_layout.setSpacing(20)
        
        # 用户名输入
        username_label = QLabel("用户名:")
        username_label.setFont(QFont("Microsoft YaHei", 10))
        username_label.setStyleSheet("color: #555;")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: #fafafa;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
        """)
        form_layout.addWidget(self.username_input)
        
        # 密码输入
        password_label = QLabel("密码:")
        password_label.setFont(QFont("Microsoft YaHei", 10))
        password_label.setStyleSheet("color: #555;")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: #fafafa;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
        """)
        form_layout.addWidget(self.password_input)
        
        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.login_button.clicked.connect(self.attempt_login)
        form_layout.addWidget(self.login_button)
        
        # 将表单居中
        form_container = QHBoxLayout()
        form_container.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        form_container.addWidget(form_frame)
        form_container.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        main_layout.addLayout(form_container)
        
        # 底部空白
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        self.setLayout(main_layout)
        
        # 设置焦点策略和事件追踪
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        
    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def enterEvent(self, event):
        """鼠标进入窗口事件"""
        self.mouse_inside = True
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开窗口事件"""
        self.mouse_inside = False
        super().leaveEvent(event)
    
    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == Qt.Key_Space:
            self.space_pressed = True
            self.check_secret_login()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.attempt_login()
        super().keyPressEvent(event)
    
    def keyReleaseEvent(self, event):
        """键盘释放事件"""
        if event.key() == Qt.Key_Space:
            self.space_pressed = False
        super().keyReleaseEvent(event)
    
    def check_secret_login(self):
        """检查秘密登录条件"""
        if self.mouse_inside and self.space_pressed:
            # 成功登录，发出信号
            self.login_success.emit()
    
    def attempt_login(self):
        """普通登录尝试（总是失败）"""
        # 这里不做任何提示，按照要求登录过程中没有任何提示
        pass


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序图标和名称
    app.setApplicationName("大型应用系统")
    
    login_window = LoginWindow()
    login_window.show()
    
    # 连接登录成功信号
    def on_login_success():
        print("登录成功！")  # 这里可以连接到主应用窗口
        login_window.close()
    
    login_window.login_success.connect(on_login_success)
    
    sys.exit(app.exec_())
